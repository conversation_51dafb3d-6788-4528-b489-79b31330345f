package com.wheat.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wheat.dto.AgriculturalTaskDTO;
import com.wheat.entity.AgriculturalTask;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wheat.vo.AgriculturalTaskVo;

/**
* <AUTHOR>
* @description 针对表【agricultural_task】的数据库操作Service
* @createDate 2025-06-23 11:10:42
*/
public interface AgriculturalTaskService extends IService<AgriculturalTask> {

    /**
     * 分页查询农业任务列表
     * @param dto 查询条件和分页参数
     * @return 分页结果
     */
    IPage<AgriculturalTaskVo> getAgriculturalTaskList(AgriculturalTaskDTO dto);

}

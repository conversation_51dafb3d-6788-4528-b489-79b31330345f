package com.wheat.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wheat.entity.AgriculturalTask;
import com.wheat.service.AgriculturalTaskService;
import com.wheat.mapper.AgriculturalTaskMapper;
import com.wheat.vo.AgriculturalTaskVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【agricultural_task】的数据库操作Service实现
* @createDate 2025-06-23 11:10:42
*/
@Service
public class AgriculturalTaskServiceImpl extends ServiceImpl<AgriculturalTaskMapper, AgriculturalTask>
    implements AgriculturalTaskService{
    @Autowired
    private AgriculturalTaskMapper agriculturalTaskMapper;

    @Override
    public IPage<AgriculturalTaskVo> getAgriculturalTaskList(Long currentPage, Long pageSize) {
        Page<AgriculturalTaskVo> page = new Page<>(currentPage, pageSize);
        return agriculturalTaskMapper.getAgriculturalTaskListPage(page);
    }
}





package com.wheat.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wheat.dto.AgriculturalTaskDTO;
import com.wheat.entity.AgriculturalTask;
import com.wheat.service.AgriculturalTaskService;
import com.wheat.mapper.AgriculturalTaskMapper;
import com.wheat.vo.AgriculturalTaskVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【agricultural_task】的数据库操作Service实现
* @createDate 2025-06-23 11:10:42
*/
@Service
public class AgriculturalTaskServiceImpl extends ServiceImpl<AgriculturalTaskMapper, AgriculturalTask>
    implements AgriculturalTaskService{
    @Autowired
    private AgriculturalTaskMapper agriculturalTaskMapper;

    @Override
    public IPage<AgriculturalTaskVo> getAgriculturalTaskList(AgriculturalTaskDTO dto) {
        // 创建分页对象
        Page<AgriculturalTaskVo> page = new Page<>(dto.getCurrent(), dto.getSize());
        // 调用 Mapper 进行分页查询，传入查询条件
        return agriculturalTaskMapper.getAgriculturalTaskListPage(page, dto);
    }
}





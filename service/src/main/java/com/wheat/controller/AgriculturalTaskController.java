package com.wheat.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wheat.dto.AgriculturalTaskDTO;
import com.wheat.exception.WheatException;
import com.wheat.result.Result;
import com.wheat.vo.AgriculturalTaskVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.wheat.service.AgriculturalTaskService;
@RestController
@RequestMapping("/api/agricultural_task")
public class AgriculturalTaskController {
    @Autowired
    private AgriculturalTaskService service;
    @PostMapping("/page")
    public Result<IPage<AgriculturalTaskVo>> getAgriculturalTaskList(@RequestBody AgriculturalTaskDTO dto){
        try{
            if(dto.getCurrent()== null || dto.getCurrent()<=0) {
                dto.setCurrent(1L);
                dto.setSize(10L);
            }
            return Result.ok(service.getAgriculturalTaskList(dto));
        }catch (WheatException e){
            return Result.fail(e.getCode(),e.getMessage());
        }

    }
}

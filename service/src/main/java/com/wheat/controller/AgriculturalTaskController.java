package com.wheat.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wheat.dto.AgriculturalTaskDTO;
import com.wheat.result.Result;
import com.wheat.vo.AgriculturalTaskVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.wheat.service.AgriculturalTaskService;
@RestController
@RequestMapping("/api/agricultural_task")
public class AgriculturalTaskController {
    @Autowired
    private AgriculturalTaskService service;
    @PostMapping("/page")
    public Result<IPage<AgriculturalTaskVo>> getAgriculturalTaskList(@RequestBody AgriculturalTaskDTO dto){
        return Result.ok(service.getAgriculturalTaskList(dto));
    }
}

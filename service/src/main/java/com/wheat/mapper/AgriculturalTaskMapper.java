package com.wheat.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wheat.dto.AgriculturalTaskDTO;
import com.wheat.entity.AgriculturalTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wheat.vo.AgriculturalTaskVo;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【agricultural_task】的数据库操作Mapper
* @createDate 2025-06-23 11:10:42
* @Entity generator.entity.AgriculturalTask
*/
public interface AgriculturalTaskMapper extends BaseMapper<AgriculturalTask> {

    /**
     * 分页查询农业任务列表
     * @param page 分页对象
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<AgriculturalTaskVo> getAgriculturalTaskListPage(@Param("page") Page<AgriculturalTaskVo> page, @Param("dto") AgriculturalTaskDTO dto);

}





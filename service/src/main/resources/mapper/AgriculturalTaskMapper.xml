<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wheat.mapper.AgriculturalTaskMapper">

    <resultMap id="BaseResultMap" type="com.wheat.entity.AgriculturalTask">
            <id property="id" column="id" />
            <result property="schemeId" column="scheme_id" />
            <result property="stage" column="stage" />
            <result property="startDate" column="start_date" />
            <result property="endDate" column="end_date" />
            <result property="waterUsage" column="water_usage" />
            <result property="nitrogenUsage" column="nitrogen_usage" />
            <result property="phosphorusUsage" column="phosphorus_usage" />
            <result property="potassiumUsage" column="potassium_usage" />
            <result property="taskName" column="task_name" />
            <result property="taskDate" column="task_date" />
            <result property="condition" column="condition" />
    </resultMap>

    <sql id="Base_Column_List">
        id,scheme_id,stage,start_date,end_date,water_usage,
        nitrogen_usage,phosphorus_usage,potassium_usage,task_name,task_date,
        condition
    </sql>
    <select id="getAgriculturalTaskListPage" resultType="com.wheat.vo.AgriculturalTaskVo">
        SELECT
            id,
            scheme_id as schemeId,
            stage,
            start_date as startDate,
            end_date as endDate,
            water_usage as waterUsage,
            nitrogen_usage as nitrogenUsage,
            phosphorus_usage as phosphorusUsage,
            potassium_usage as potassiumUsage,
            task_name as taskName,
            task_date as taskDate,
            `condition`
        FROM wheat_agricultural_task
        <where>
            <if test="dto.id != null">
                AND id = #{dto.id}
            </if>
            <if test="dto.schemeId != null">
                AND scheme_id = #{dto.schemeId}
            </if>
            <if test="dto.stage != null and dto.stage != ''">
                AND stage LIKE CONCAT('%', #{dto.stage}, '%')
            </if>
            <if test="dto.taskName != null and dto.taskName != ''">
                AND task_name LIKE CONCAT('%', #{dto.taskName}, '%')
            </if>
            <if test="dto.startDate != null">
                AND start_date >= #{dto.startDate}
            </if>
            <if test="dto.endDate != null">
                AND end_date &lt;= #{dto.endDate}
            </if>
            <if test="dto.taskDate != null">
                AND task_date = #{dto.taskDate}
            </if>
            <if test="dto.condition != null and dto.condition != ''">
                AND `condition` LIKE CONCAT('%', #{dto.condition}, '%')
            </if>
        </where>
        ORDER BY id DESC
    </select>
</mapper>

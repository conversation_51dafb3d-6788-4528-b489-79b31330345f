package com.wheat.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 
 * @TableName agricultural_task
 */
@Data
@TableName(value="agricultural_task")
public class AgriculturalTask {
    /**
     * 
     */
    private Integer id;

    /**
     * 
     */
    private Integer schemeId;

    /**
     * 
     */
    private String stage;

    /**
     * 
     */
    private Date startDate;

    /**
     * 
     */
    private Date endDate;

    /**
     * 
     */
    private Double waterUsage;

    /**
     * 
     */
    private Double nitrogenUsage;

    /**
     * 
     */
    private Double phosphorusUsage;

    /**
     * 
     */
    private Double potassiumUsage;

    /**
     * 
     */
    private String taskName;

    /**
     * 
     */
    private Date taskDate;

    /**
     * 
     */
    private String condition;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        AgriculturalTask other = (AgriculturalTask) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getSchemeId() == null ? other.getSchemeId() == null : this.getSchemeId().equals(other.getSchemeId()))
            && (this.getStage() == null ? other.getStage() == null : this.getStage().equals(other.getStage()))
            && (this.getStartDate() == null ? other.getStartDate() == null : this.getStartDate().equals(other.getStartDate()))
            && (this.getEndDate() == null ? other.getEndDate() == null : this.getEndDate().equals(other.getEndDate()))
            && (this.getWaterUsage() == null ? other.getWaterUsage() == null : this.getWaterUsage().equals(other.getWaterUsage()))
            && (this.getNitrogenUsage() == null ? other.getNitrogenUsage() == null : this.getNitrogenUsage().equals(other.getNitrogenUsage()))
            && (this.getPhosphorusUsage() == null ? other.getPhosphorusUsage() == null : this.getPhosphorusUsage().equals(other.getPhosphorusUsage()))
            && (this.getPotassiumUsage() == null ? other.getPotassiumUsage() == null : this.getPotassiumUsage().equals(other.getPotassiumUsage()))
            && (this.getTaskName() == null ? other.getTaskName() == null : this.getTaskName().equals(other.getTaskName()))
            && (this.getTaskDate() == null ? other.getTaskDate() == null : this.getTaskDate().equals(other.getTaskDate()))
            && (this.getCondition() == null ? other.getCondition() == null : this.getCondition().equals(other.getCondition()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getSchemeId() == null) ? 0 : getSchemeId().hashCode());
        result = prime * result + ((getStage() == null) ? 0 : getStage().hashCode());
        result = prime * result + ((getStartDate() == null) ? 0 : getStartDate().hashCode());
        result = prime * result + ((getEndDate() == null) ? 0 : getEndDate().hashCode());
        result = prime * result + ((getWaterUsage() == null) ? 0 : getWaterUsage().hashCode());
        result = prime * result + ((getNitrogenUsage() == null) ? 0 : getNitrogenUsage().hashCode());
        result = prime * result + ((getPhosphorusUsage() == null) ? 0 : getPhosphorusUsage().hashCode());
        result = prime * result + ((getPotassiumUsage() == null) ? 0 : getPotassiumUsage().hashCode());
        result = prime * result + ((getTaskName() == null) ? 0 : getTaskName().hashCode());
        result = prime * result + ((getTaskDate() == null) ? 0 : getTaskDate().hashCode());
        result = prime * result + ((getCondition() == null) ? 0 : getCondition().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", schemeId=").append(schemeId);
        sb.append(", stage=").append(stage);
        sb.append(", startDate=").append(startDate);
        sb.append(", endDate=").append(endDate);
        sb.append(", waterUsage=").append(waterUsage);
        sb.append(", nitrogenUsage=").append(nitrogenUsage);
        sb.append(", phosphorusUsage=").append(phosphorusUsage);
        sb.append(", potassiumUsage=").append(potassiumUsage);
        sb.append(", taskName=").append(taskName);
        sb.append(", taskDate=").append(taskDate);
        sb.append(", condition=").append(condition);
        sb.append("]");
        return sb.toString();
    }
}